import { BibleBook } from '../types';

export const bibleBooks: BibleBook[] = [
  // Old Testament
  {
    id: 'genesis',
    name: '<PERSON>',
    testament: 'old',
    chapters: 50,
    description: 'The book of beginnings - creation, fall, flood, and the patriarchs',
    slug: 'genesis'
  },
  {
    id: 'exodus',
    name: 'Exodus',
    testament: 'old',
    chapters: 40,
    description: 'The deliverance of Israel from Egypt and the giving of the Law',
    slug: 'exodus'
  },
  {
    id: 'leviticus',
    name: '<PERSON><PERSON>',
    testament: 'old',
    chapters: 27,
    description: 'Laws for worship, sacrifice, and holy living',
    slug: 'leviticus'
  },
  {
    id: 'numbers',
    name: 'Numbers',
    testament: 'old',
    chapters: 36,
    description: 'Israel\'s wilderness wanderings and preparation for the Promised Land',
    slug: 'numbers'
  },
  {
    id: 'deuteronomy',
    name: 'Deuteronomy',
    testament: 'old',
    chapters: 34,
    description: '<PERSON>\' final speeches and the renewal of the covenant',
    slug: 'deuteronomy'
  },
  {
    id: 'joshua',
    name: '<PERSON>',
    testament: 'old',
    chapters: 24,
    description: 'The conquest and settlement of the Promised Land',
    slug: 'joshua'
  },
  {
    id: 'judges',
    name: 'Judges',
    testament: 'old',
    chapters: 21,
    description: 'The cycle of sin, oppression, and deliverance in early Israel',
    slug: 'judges'
  },
  {
    id: 'ruth',
    name: '<PERSON>',
    testament: 'old',
    chapters: 4,
    description: 'A beautiful story of loyalty, love, and redemption',
    slug: 'ruth'
  },
  {
    id: '1-samuel',
    name: '1 Samuel',
    testament: 'old',
    chapters: 31,
    description: 'The transition from judges to kings - Samuel, Saul, and David',
    slug: '1-samuel'
  },
  {
    id: '2-samuel',
    name: '2 Samuel',
    testament: 'old',
    chapters: 24,
    description: 'The reign of King David and the establishment of his dynasty',
    slug: '2-samuel'
  },
  {
    id: '1-kings',
    name: '1 Kings',
    testament: 'old',
    chapters: 22,
    description: 'Solomon\'s reign and the division of the kingdom',
    slug: '1-kings'
  },
  {
    id: '2-kings',
    name: '2 Kings',
    testament: 'old',
    chapters: 25,
    description: 'The decline and fall of Israel and Judah',
    slug: '2-kings'
  },
  {
    id: '1-chronicles',
    name: '1 Chronicles',
    testament: 'old',
    chapters: 29,
    description: 'A priestly perspective on Israel\'s history from Adam to David',
    slug: '1-chronicles'
  },
  {
    id: '2-chronicles',
    name: '2 Chronicles',
    testament: 'old',
    chapters: 36,
    description: 'The history of Judah from Solomon to the exile',
    slug: '2-chronicles'
  },
  {
    id: 'ezra',
    name: 'Ezra',
    testament: 'old',
    chapters: 10,
    description: 'The return from exile and the rebuilding of the temple',
    slug: 'ezra'
  },
  {
    id: 'nehemiah',
    name: 'Nehemiah',
    testament: 'old',
    chapters: 13,
    description: 'The rebuilding of Jerusalem\'s walls and spiritual renewal',
    slug: 'nehemiah'
  },
  {
    id: 'esther',
    name: 'Esther',
    testament: 'old',
    chapters: 10,
    description: 'God\'s providence in preserving His people through Queen Esther',
    slug: 'esther'
  },
  {
    id: 'job',
    name: 'Job',
    testament: 'old',
    chapters: 42,
    description: 'The problem of suffering and God\'s sovereignty',
    slug: 'job'
  },
  {
    id: 'psalms',
    name: 'Psalms',
    testament: 'old',
    chapters: 150,
    description: 'Songs of worship, praise, and prayer',
    slug: 'psalms'
  },
  {
    id: 'proverbs',
    name: 'Proverbs',
    testament: 'old',
    chapters: 31,
    description: 'Wisdom for daily living and godly character',
    slug: 'proverbs'
  },
  {
    id: 'ecclesiastes',
    name: 'Ecclesiastes',
    testament: 'old',
    chapters: 12,
    description: 'The search for meaning and purpose in life',
    slug: 'ecclesiastes'
  },
  {
    id: 'song-of-solomon',
    name: 'Song of Solomon',
    testament: 'old',
    chapters: 8,
    description: 'A celebration of love and marriage',
    slug: 'song-of-solomon'
  },
  {
    id: 'isaiah',
    name: 'Isaiah',
    testament: 'old',
    chapters: 66,
    description: 'Prophecies of judgment and salvation, including the Messiah',
    slug: 'isaiah'
  },
  {
    id: 'jeremiah',
    name: 'Jeremiah',
    testament: 'old',
    chapters: 52,
    description: 'The weeping prophet\'s call to repentance and hope',
    slug: 'jeremiah'
  },
  {
    id: 'lamentations',
    name: 'Lamentations',
    testament: 'old',
    chapters: 5,
    description: 'Mourning over Jerusalem\'s destruction',
    slug: 'lamentations'
  },
  {
    id: 'ezekiel',
    name: 'Ezekiel',
    testament: 'old',
    chapters: 48,
    description: 'Visions of God\'s glory and the restoration of Israel',
    slug: 'ezekiel'
  },
  {
    id: 'daniel',
    name: 'Daniel',
    testament: 'old',
    chapters: 12,
    description: 'Faithfulness in exile and visions of the future',
    slug: 'daniel'
  },
  {
    id: 'hosea',
    name: 'Hosea',
    testament: 'old',
    chapters: 14,
    description: 'God\'s unfailing love despite Israel\'s unfaithfulness',
    slug: 'hosea'
  },
  {
    id: 'joel',
    name: 'Joel',
    testament: 'old',
    chapters: 3,
    description: 'The Day of the Lord and the outpouring of God\'s Spirit',
    slug: 'joel'
  },
  {
    id: 'amos',
    name: 'Amos',
    testament: 'old',
    chapters: 9,
    description: 'A call for justice and righteousness',
    slug: 'amos'
  },
  {
    id: 'obadiah',
    name: 'Obadiah',
    testament: 'old',
    chapters: 1,
    description: 'Judgment against Edom for their pride and cruelty',
    slug: 'obadiah'
  },
  {
    id: 'jonah',
    name: 'Jonah',
    testament: 'old',
    chapters: 4,
    description: 'God\'s mercy extends to all nations, even enemies',
    slug: 'jonah'
  },
  {
    id: 'micah',
    name: 'Micah',
    testament: 'old',
    chapters: 7,
    description: 'Justice, mercy, and walking humbly with God',
    slug: 'micah'
  },
  {
    id: 'nahum',
    name: 'Nahum',
    testament: 'old',
    chapters: 3,
    description: 'The fall of Nineveh and God\'s justice',
    slug: 'nahum'
  },
  {
    id: 'habakkuk',
    name: 'Habakkuk',
    testament: 'old',
    chapters: 3,
    description: 'Wrestling with God\'s justice and living by faith',
    slug: 'habakkuk'
  },
  {
    id: 'zephaniah',
    name: 'Zephaniah',
    testament: 'old',
    chapters: 3,
    description: 'The Day of the Lord and the promise of restoration',
    slug: 'zephaniah'
  },
  {
    id: 'haggai',
    name: 'Haggai',
    testament: 'old',
    chapters: 2,
    description: 'Encouragement to rebuild the temple',
    slug: 'haggai'
  },
  {
    id: 'zechariah',
    name: 'Zechariah',
    testament: 'old',
    chapters: 14,
    description: 'Visions of hope and the coming Messiah',
    slug: 'zechariah'
  },
  {
    id: 'malachi',
    name: 'Malachi',
    testament: 'old',
    chapters: 4,
    description: 'The final Old Testament prophet\'s call to faithfulness',
    slug: 'malachi'
  },
  // New Testament
  {
    id: 'matthew',
    name: 'Matthew',
    testament: 'new',
    chapters: 28,
    description: 'Jesus as the promised Messiah and King',
    slug: 'matthew'
  },
  {
    id: 'mark',
    name: 'Mark',
    testament: 'new',
    chapters: 16,
    description: 'Jesus as the suffering Servant',
    slug: 'mark'
  },
  {
    id: 'luke',
    name: 'Luke',
    testament: 'new',
    chapters: 24,
    description: 'Jesus as the perfect Son of Man',
    slug: 'luke'
  },
  {
    id: 'john',
    name: 'John',
    testament: 'new',
    chapters: 21,
    description: 'Jesus as the Son of God and the Word made flesh',
    slug: 'john'
  },
  {
    id: 'acts',
    name: 'Acts',
    testament: 'new',
    chapters: 28,
    description: 'The birth and growth of the early church',
    slug: 'acts'
  },
  {
    id: 'romans',
    name: 'Romans',
    testament: 'new',
    chapters: 16,
    description: 'The gospel of salvation by grace through faith',
    slug: 'romans'
  },
  {
    id: '1-corinthians',
    name: '1 Corinthians',
    testament: 'new',
    chapters: 16,
    description: 'Addressing problems in the Corinthian church',
    slug: '1-corinthians'
  },
  {
    id: '2-corinthians',
    name: '2 Corinthians',
    testament: 'new',
    chapters: 13,
    description: 'Paul\'s defense of his apostolic ministry',
    slug: '2-corinthians'
  },
  {
    id: 'galatians',
    name: 'Galatians',
    testament: 'new',
    chapters: 6,
    description: 'Freedom from the law through faith in Christ',
    slug: 'galatians'
  },
  {
    id: 'ephesians',
    name: 'Ephesians',
    testament: 'new',
    chapters: 6,
    description: 'Our identity and unity in Christ',
    slug: 'ephesians'
  },
  {
    id: 'philippians',
    name: 'Philippians',
    testament: 'new',
    chapters: 4,
    description: 'Joy and contentment in Christ',
    slug: 'philippians'
  },
  {
    id: 'colossians',
    name: 'Colossians',
    testament: 'new',
    chapters: 4,
    description: 'The supremacy and sufficiency of Christ',
    slug: 'colossians'
  },
  {
    id: '1-thessalonians',
    name: '1 Thessalonians',
    testament: 'new',
    chapters: 5,
    description: 'Living in light of Christ\'s return',
    slug: '1-thessalonians'
  },
  {
    id: '2-thessalonians',
    name: '2 Thessalonians',
    testament: 'new',
    chapters: 3,
    description: 'Correction about the Day of the Lord',
    slug: '2-thessalonians'
  },
  {
    id: '1-timothy',
    name: '1 Timothy',
    testament: 'new',
    chapters: 6,
    description: 'Instructions for church leadership and conduct',
    slug: '1-timothy'
  },
  {
    id: '2-timothy',
    name: '2 Timothy',
    testament: 'new',
    chapters: 4,
    description: 'Paul\'s final charge to Timothy',
    slug: '2-timothy'
  },
  {
    id: 'titus',
    name: 'Titus',
    testament: 'new',
    chapters: 3,
    description: 'Qualifications for church leaders and good works',
    slug: 'titus'
  },
  {
    id: 'philemon',
    name: 'Philemon',
    testament: 'new',
    chapters: 1,
    description: 'Forgiveness and reconciliation in Christ',
    slug: 'philemon'
  },
  {
    id: 'hebrews',
    name: 'Hebrews',
    testament: 'new',
    chapters: 13,
    description: 'The superiority of Christ over the old covenant',
    slug: 'hebrews'
  },
  {
    id: 'james',
    name: 'James',
    testament: 'new',
    chapters: 5,
    description: 'Practical wisdom for Christian living',
    slug: 'james'
  },
  {
    id: '1-peter',
    name: '1 Peter',
    testament: 'new',
    chapters: 5,
    description: 'Hope and perseverance through suffering',
    slug: '1-peter'
  },
  {
    id: '2-peter',
    name: '2 Peter',
    testament: 'new',
    chapters: 3,
    description: 'Warning against false teachers and the promise of Christ\'s return',
    slug: '2-peter'
  },
  {
    id: '1-john',
    name: '1 John',
    testament: 'new',
    chapters: 5,
    description: 'Assurance of salvation and the love of God',
    slug: '1-john'
  },
  {
    id: '2-john',
    name: '2 John',
    testament: 'new',
    chapters: 1,
    description: 'Walking in truth and love',
    slug: '2-john'
  },
  {
    id: '3-john',
    name: '3 John',
    testament: 'new',
    chapters: 1,
    description: 'Hospitality and supporting fellow workers',
    slug: '3-john'
  },
  {
    id: 'jude',
    name: 'Jude',
    testament: 'new',
    chapters: 1,
    description: 'Contending for the faith against false teachers',
    slug: 'jude'
  },
  {
    id: 'revelation',
    name: 'Revelation',
    testament: 'new',
    chapters: 22,
    description: 'The ultimate victory of Christ and the new heaven and earth',
    slug: 'revelation'
  }
];

export const oldTestamentBooks = bibleBooks.filter(book => book.testament === 'old');
export const newTestamentBooks = bibleBooks.filter(book => book.testament === 'new');
