import { genesisQuestions, matthewQuestions, allQuestions } from '@/lib/data/quiz-questions';
import { bibleBooks } from '@/lib/data/bible-books';
import { notFound } from 'next/navigation';
import { BookQuizClient } from './BookQuizClient';

interface BookQuizPageProps {
  params: {
    book: string;
  };
}

export async function generateStaticParams() {
  return bibleBooks.map((book) => ({
    book: book.slug,
  }));
}

export default function BookQuizPage({ params }: BookQuizPageProps) {
  const book = bibleBooks.find(b => b.slug === params.book);

  if (!book) {
    notFound();
  }

  // Get questions for this book (for now, use sample questions)
  const getQuestionsForBook = (bookSlug: string) => {
    switch (bookSlug) {
      case 'genesis':
        return genesisQuestions;
      case 'matthew':
        return matthewQuestions;
      default:
        // Return a subset of all questions for other books
        return allQuestions.filter(q => q.book === bookSlug).slice(0, 5);
    }
  };

  const questions = getQuestionsForBook(params.book);

  return (
    <BookQuizClient book={book} questions={questions} />
  );
}
