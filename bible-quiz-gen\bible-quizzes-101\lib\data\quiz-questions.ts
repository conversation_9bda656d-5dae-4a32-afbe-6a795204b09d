import { QuizQuestion } from '../types';

export const genesisQuestions: QuizQuestion[] = [
  {
    id: 'gen-1-1',
    question: 'What did God create on the first day?',
    options: ['Light', 'Sky', 'Land and seas', 'Sun and moon'],
    correctAnswer: 0,
    explanation: 'God said "Let there be light" on the first day of creation.',
    difficulty: 'easy',
    category: 'book',
    book: 'genesis',
    chapter: 1,
    verse: 'Genesis 1:3'
  },
  {
    id: 'gen-1-2',
    question: 'How many days did it take God to create the world?',
    options: ['5 days', '6 days', '7 days', '8 days'],
    correctAnswer: 1,
    explanation: 'God created the world in 6 days and rested on the 7th day.',
    difficulty: 'easy',
    category: 'book',
    book: 'genesis',
    chapter: 1,
    verse: 'Genesis 1:31-2:2'
  },
  {
    id: 'gen-2-1',
    question: 'What was the name of the first man?',
    options: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    correctAnswer: 2,
    explanation: 'God formed man from the dust of the ground and named him <PERSON>.',
    difficulty: 'easy',
    category: 'book',
    book: 'genesis',
    chapter: 2,
    verse: 'Genesis 2:7'
  },
  {
    id: 'gen-3-1',
    question: 'What fruit did <PERSON> and <PERSON> eat that was forbidden?',
    options: ['Apple', 'Grape', 'Fig', 'The fruit of the tree of knowledge of good and evil'],
    correctAnswer: 3,
    explanation: 'The Bible doesn\'t specify the type of fruit, only that it was from the tree of knowledge of good and evil.',
    difficulty: 'medium',
    category: 'book',
    book: 'genesis',
    chapter: 3,
    verse: 'Genesis 3:6'
  },
  {
    id: 'gen-6-1',
    question: 'Who built the ark?',
    options: ['Moses', 'Abraham', 'Noah', 'David'],
    correctAnswer: 2,
    explanation: 'God commanded Noah to build an ark to save his family and the animals from the flood.',
    difficulty: 'easy',
    category: 'book',
    book: 'genesis',
    chapter: 6,
    verse: 'Genesis 6:14'
  }
];

export const matthewQuestions: QuizQuestion[] = [
  {
    id: 'mat-1-1',
    question: 'What does the name "Jesus" mean?',
    options: ['King', 'Savior', 'Teacher', 'Prophet'],
    correctAnswer: 1,
    explanation: 'The angel told Joseph that Jesus would save His people from their sins.',
    difficulty: 'medium',
    category: 'book',
    book: 'matthew',
    chapter: 1,
    verse: 'Matthew 1:21'
  },
  {
    id: 'mat-5-1',
    question: 'According to the Beatitudes, who are blessed?',
    options: ['The rich', 'The poor in spirit', 'The powerful', 'The famous'],
    correctAnswer: 1,
    explanation: 'Jesus said "Blessed are the poor in spirit, for theirs is the kingdom of heaven."',
    difficulty: 'easy',
    category: 'book',
    book: 'matthew',
    chapter: 5,
    verse: 'Matthew 5:3'
  },
  {
    id: 'mat-6-1',
    question: 'What prayer did Jesus teach His disciples?',
    options: ['The Apostles\' Creed', 'The Lord\'s Prayer', 'The Serenity Prayer', 'The Prayer of Jabez'],
    correctAnswer: 1,
    explanation: 'Jesus taught His disciples the Lord\'s Prayer, beginning with "Our Father in heaven."',
    difficulty: 'easy',
    category: 'book',
    book: 'matthew',
    chapter: 6,
    verse: 'Matthew 6:9-13'
  }
];

export const characterQuestions: QuizQuestion[] = [
  {
    id: 'char-moses-1',
    question: 'Where did Moses receive the Ten Commandments?',
    options: ['Mount Sinai', 'Mount Carmel', 'Mount Olive', 'Mount Zion'],
    correctAnswer: 0,
    explanation: 'Moses received the Ten Commandments from God on Mount Sinai.',
    difficulty: 'easy',
    category: 'character',
    verse: 'Exodus 19:20'
  },
  {
    id: 'char-david-1',
    question: 'What giant did David defeat?',
    options: ['Og', 'Goliath', 'Anak', 'Rephaim'],
    correctAnswer: 1,
    explanation: 'David defeated the Philistine giant Goliath with a sling and stone.',
    difficulty: 'easy',
    category: 'character',
    verse: '1 Samuel 17:49'
  },
  {
    id: 'char-paul-1',
    question: 'What was Paul\'s name before his conversion?',
    options: ['Simon', 'Saul', 'Samuel', 'Solomon'],
    correctAnswer: 1,
    explanation: 'Paul was originally named Saul and persecuted Christians before his conversion.',
    difficulty: 'medium',
    category: 'character',
    verse: 'Acts 9:1-4'
  }
];

export const themeQuestions: QuizQuestion[] = [
  {
    id: 'theme-faith-1',
    question: 'According to Hebrews 11:1, what is faith?',
    options: [
      'Believing without seeing',
      'The substance of things hoped for, the evidence of things not seen',
      'Trust in God',
      'Following Jesus'
    ],
    correctAnswer: 1,
    explanation: 'Hebrews 11:1 defines faith as "the substance of things hoped for, the evidence of things not seen."',
    difficulty: 'medium',
    category: 'theme',
    verse: 'Hebrews 11:1'
  },
  {
    id: 'theme-love-1',
    question: 'What is the greatest commandment according to Jesus?',
    options: [
      'Do not steal',
      'Love the Lord your God with all your heart, soul, and mind',
      'Honor your father and mother',
      'Do not murder'
    ],
    correctAnswer: 1,
    explanation: 'Jesus said the greatest commandment is to love God with all your heart, soul, and mind.',
    difficulty: 'easy',
    category: 'theme',
    verse: 'Matthew 22:37'
  }
];

export const gospelQuestions: QuizQuestion[] = [
  {
    id: 'gospel-1',
    question: 'How many Gospels are there in the New Testament?',
    options: ['3', '4', '5', '6'],
    correctAnswer: 1,
    explanation: 'There are four Gospels: Matthew, Mark, Luke, and John.',
    difficulty: 'easy',
    category: 'gospel',
    verse: 'New Testament'
  },
  {
    id: 'gospel-2',
    question: 'Which Gospel was written by a doctor?',
    options: ['Matthew', 'Mark', 'Luke', 'John'],
    correctAnswer: 2,
    explanation: 'Luke was a physician and wrote the Gospel of Luke and the book of Acts.',
    difficulty: 'medium',
    category: 'gospel',
    verse: 'Colossians 4:14'
  }
];

export const allQuestions = [
  ...genesisQuestions,
  ...matthewQuestions,
  ...characterQuestions,
  ...themeQuestions,
  ...gospelQuestions
];
