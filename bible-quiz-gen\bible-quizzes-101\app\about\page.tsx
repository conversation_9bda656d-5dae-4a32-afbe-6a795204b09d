import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Link from 'next/link';

export default function AboutPage() {
  const features = [
    {
      title: "Comprehensive Coverage",
      description: "Quizzes for all 66 books of the Bible, from Genesis to Revelation",
      icon: "📚"
    },
    {
      title: "Multiple Difficulty Levels",
      description: "Easy, medium, and hard questions to challenge learners at every level",
      icon: "🎯"
    },
    {
      title: "Character Studies",
      description: "Learn about biblical figures from <PERSON> to the Apostles",
      icon: "👥"
    },
    {
      title: "Thematic Learning",
      description: "Explore biblical themes like faith, love, salvation, and more",
      icon: "💡"
    },
    {
      title: "Instant Results",
      description: "Get immediate feedback with explanations for each answer",
      icon: "⚡"
    },
    {
      title: "No Registration Required",
      description: "Start learning immediately without creating an account",
      icon: "🚀"
    }
  ];

  const stats = [
    { label: "Bible Books Covered", value: "66" },
    { label: "Biblical Characters", value: "20+" },
    { label: "Themes Explored", value: "20+" },
    { label: "Total Questions", value: "1000+" }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              About BibleQuizNow
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              We&apos;re passionate about making Bible learning fun, accessible, and engaging for everyone.
              Our interactive quizzes help you test your knowledge, learn new facts, and grow in your faith.
            </p>
            <Link href="/quizzes">
              <Button size="lg">Start Your First Quiz</Button>
            </Link>
          </div>
          <div className="flex justify-center">
            <Image
              src="/images/study-group.svg"
              alt="Bible Study Group"
              width={500}
              height={350}
              className="max-w-full h-auto"
            />
          </div>
        </div>

        {/* Mission Statement */}
        <section className="mb-16">
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-blue-900">Our Mission</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-lg text-blue-800 max-w-4xl mx-auto">
&ldquo;Your word is a lamp for my feet, a light on my path.&rdquo; - Psalm 119:105
              </p>
              <p className="text-blue-700 mt-4 max-w-4xl mx-auto">
                Our mission is to illuminate God&apos;s Word through interactive learning experiences.
                We believe that studying the Bible should be engaging, accessible, and enjoyable for people
                of all ages and backgrounds. Through our comprehensive quiz platform, we aim to help
                individuals deepen their understanding of Scripture and grow in their faith journey.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Features */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose BibleQuizNow?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover what makes our platform the perfect choice for Bible learning
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Stats */}
        <section className="mb-16">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                By the Numbers
              </h2>
              <p className="text-gray-600">
                Our comprehensive coverage of biblical content
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl font-bold text-blue-600 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Getting started is simple and straightforward
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Choose a Quiz</h3>
              <p className="text-gray-600">
                Select from books, characters, themes, or specific topics that interest you
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Answer Questions</h3>
              <p className="text-gray-600">
                Work through multiple-choice questions at your own pace
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Learn & Grow</h3>
              <p className="text-gray-600">
                Review your results, read explanations, and share your achievements
              </p>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-12">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Begin Your Bible Learning Journey?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Join thousands of others in exploring God&apos;s Word through interactive quizzes
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/quizzes">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">Start Learning Now</Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">Contact Us</Button>
            </Link>
          </div>
        </section>
      </div>
    </div>
  );
}
